<template>
    <div class="space-y-16 sm:space-y-25">
        <HeroBanner v-bind="homeData || {}" />
        <ClientOnly>
            <TopCoin />
            <template #fallback>
                <div class="container grid xl:grid-cols-3 gap-14">
                    <div class="xl:col-span-2 space-y-4">
                        <div class="h-8 bg-bg-2 animate-pulse rounded w-48"></div>
                        <div class="border border-bg-4 rounded-lg p-2 h-96 bg-bg-2 animate-pulse"></div>
                    </div>
                    <div class="xl:col-span-1 space-y-4">
                        <div class="h-8 bg-bg-2 animate-pulse rounded w-32"></div>
                        <div class="space-y-4 h-96">
                            <div class="h-4 bg-bg-2 animate-pulse rounded"></div>
                            <div class="h-4 bg-bg-2 animate-pulse rounded w-3/4"></div>
                            <div class="h-4 bg-bg-2 animate-pulse rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </template>
        </ClientOnly>
        <Figures v-bind="homeData || {}" />
        <BannerCTADownload v-bind="homeData || {}" />
        <Services v-bind="homeData || {}" />
        <BannerCTAGetToken v-bind="homeData || {}" />
        <TradeNow />
        <AppSeo collection="homepage" />
    </div>
</template>

<script setup lang="ts">
import HeroBanner from "./components/HeroBanner.vue";
import TopCoin from "./components/TopCoin.vue";
import Figures from "./components/Figures.vue";
import BannerCTADownload from "./components/BannerCTADownload.vue";
import Services from "./components/Services.vue";
import BannerCTAGetToken from "./components/BannerCTAGetToken.vue";
import TradeNow from "./components/TradeNow.vue";

import type { Homepage, HomepageTranslation } from "#shared/types/directus-types";

const { localeProperties } = useI18n();

type Response = Homepage &
    HomepageTranslation & {
        banner_image_light: string;
        banner_image_dark: string;
        banner_download_light: string;
        banner_download_dark: string;
        image_qr_download: string;
    };

// Use useState to ensure consistent state between server and client
const homeData = useState<Response | null>('homepage-data', () => null);

// Fetch data only if not already loaded
if (!homeData.value) {
    const { data, error } = await useFetch<Response>(
        `/api/content/homepage/singleton/${localeProperties.value.name}`,
        {
            params: { fields: ["*", "translations.*"] },
            key: 'homepage-fetch'
        },
    );

    if (error.value) {
        throw createError({
            statusCode: 500,
            statusMessage: "Failed to fetch homepage data",
        });
    }

    homeData.value = data.value || null;
}
</script>
